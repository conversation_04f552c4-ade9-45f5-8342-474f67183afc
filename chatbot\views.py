from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from urllib.parse import unquote
from datetime import datetime
import weaviate
#from openai import OpenAI
from .models import PdfFile
from .upload import upload_pdf_to_db
import logging


# chatbot/views.py

import json
import os
from django.http import JsonResponse, HttpResponseForbidden, HttpResponseBadRequest
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.views import View



# views.py
# chatbot/views.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import PromptTemplate
from .serializers import PromptTemplateSerializer



import json
import os
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime
from .models import SupportTicket
from scipy.spatial.distance import cosine
from django.utils import timezone
from datetime import timedelta
from .models import SupportTicket  # Make sure this import exists
import openai




AUTO_CLOSE_DAYS = 7  # days after which tickets auto-close

def auto_close_expired_tickets():
    """Close tickets older than AUTO_CLOSE_DAYS (default: 7 days)."""
    cutoff = timezone.now() - timedelta(days=AUTO_CLOSE_DAYS)
    expired = SupportTicket.objects.filter(status="open", created_at__lt=cutoff)
    count = expired.update(status="closed")
    if count:
        print(f"✅ Auto-closed {count} expired ticket(s).")

# --- pending tickets -------------------------------------------------

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def open_tickets(request):
    """
    Return all OPEN tickets for the logged-in user,
    each with a one-line 'summary' (added in serializer earlier).
    """
    auto_close_expired_tickets()          # ⏱ keep DB tidy

    tickets = SupportTicket.objects.filter(
        user=request.user,
        status="open"
    ).order_by('-last_activity')          # newest first

    serializer = SupportTicketSerializer(tickets, many=True)
    return Response(serializer.data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ticket_detail(request, ticket_number):
    """
    Fetch one ticket (only if it belongs to this user).
    Front-end uses this when the user clicks a ticket to resume it.
    """
    auto_close_expired_tickets()

    ticket = get_object_or_404(
        SupportTicket,
        user=request.user,
        ticket_number=ticket_number
    )
    ser = SupportTicketSerializer(ticket)
    return Response(ser.data)


# --- Pending-ticket list ---------------------------------------
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import SupportTicket

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pending_tickets(request):
    tickets = []
    user = request.user

    open_tickets = SupportTicket.objects.filter(user=user, status="open").order_by('-last_activity')
    for t in open_tickets:
        desc_lines = (t.problem_description or "").splitlines()
        issue = desc_lines[0][:60] if desc_lines else "No description"

        tickets.append({
            "ticket_number": t.ticket_number,
            "status": t.status,
            "issue": issue,
            "title": t.short_title or f"{t.product_name} - {t.model}" if t.product_name and t.model else "No title",
            "short_title": t.short_title or "No title",
            "problem_description": t.problem_description or "No description available",
            "product_name": t.product_name,
            "model": t.model,
            "created_at": t.created_at,
            "last_activity": t.last_activity,
        })

    return Response({"tickets": tickets})




from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from .models import SupportTicket
from .serializers import SupportTicketSerializer

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def ticket_summary(request):
    """
    Return a summary of the ticket's problem, solution, and status.
    """
    ticket_number = request.data.get("ticket_number")
    if not ticket_number:
        return Response({"error": "Ticket number is required."}, status=status.HTTP_400_BAD_REQUEST)

    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=status.HTTP_404_NOT_FOUND)

    serializer = SupportTicketSerializer(ticket)
    return Response(serializer.data)

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.timezone import now
from datetime import timedelta
from chatbot.models import SupportTicket

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pending_ticket_summaries(request):
    """
    Return list of user's open tickets created within last 7 days,
    each with ticket_number and short_title.
    """
    seven_days_ago = now() - timedelta(days=7)

    tickets = (
        SupportTicket.objects
        .filter(user=request.user, status="open", created_at__gte=seven_days_ago)
        .order_by('-created_at')
        .values('ticket_number', 'short_title')
    )

    ticket_list = [
        {
            "ticket_number": t['ticket_number'],
            "short_title": t['short_title'] if t['short_title'] else "No title"
        }
        for t in tickets
    ]

    if not ticket_list:
        return Response({"message": "No pending tickets found."})

    return Response(ticket_list)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def start_ticket_session(request, ticket_number):
    """
    Start a chatbot session with a specific ticket.
    Returns ticket details and initial message for the chatbot.
    """
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=404)

    if ticket.status == "closed":
        return Response({"error": "Cannot start session with a closed ticket."}, status=400)

    serializer = SupportTicketSerializer(ticket)

    # Prepare initial message based on ticket state
    if ticket.problem_description:
        initial_message = f"Welcome back to ticket #{ticket.ticket_number}. You can ask follow-up questions about your issue or request additional help."
    else:
        initial_message = f"Ticket #{ticket.ticket_number} has been created with your product details. Please describe the issue you're experiencing."

    return Response({
        "ticket": serializer.data,
        "initial_message": initial_message,
        "session_type": "ticket",
        "can_add_problem": not bool(ticket.problem_description)
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def start_general_session(request):
    """
    Start a general chatbot session without ticket context.
    """
    return Response({
        "initial_message": "Hello! I'm here to help with general questions about our products and services. How can I assist you today?",
        "session_type": "general",
        "ticket": None
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def end_session(request):
    """
    End the current session and optionally close ticket.
    This can be used for cleanup when user logs out or session ends.
    """
    ticket_number = request.data.get("ticket_number")
    close_ticket = request.data.get("close_ticket", False)

    if ticket_number and close_ticket:
        try:
            ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
            if ticket.status == "open":
                ticket.status = "closed"
                ticket.save(update_fields=["status"])
                return Response({"message": f"Session ended and ticket {ticket_number} closed."})
        except SupportTicket.DoesNotExist:
            pass

    return Response({"message": "Session ended successfully."})


PROMPTS_JSON_PATH = r"D:\AI-Agent-Chatbot-main\chatbot\prompt.json"

class PromptTemplateView(APIView):
    permission_classes = [AllowAny]  # Allow unauthenticated access for prompt templates

    def get(self, request):
        prompt_type = request.query_params.get("type")
        if not prompt_type:
            return Response({"error": "Prompt type is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with open(PROMPTS_JSON_PATH, "r") as f:
                prompts = json.load(f)
        except FileNotFoundError:
            return Response({"error": "Prompts file not found"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except json.JSONDecodeError:
            return Response({"error": "Prompts file is invalid JSON"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        prompt = prompts.get(prompt_type)
        if not prompt or not prompt.get("is_active"):
            return Response({"error": "No active prompt found"}, status=status.HTTP_404_NOT_FOUND)

        return Response({
            "name": prompt_type,
            "template": prompt.get("template"),
            "is_active": prompt.get("is_active"),
            "last_modified": prompt.get("last_modified"),
        })

    def post(self, request):
        data = request.data
        prompt_type = data.get("prompt_type")
        template = data.get("template")
        is_active = data.get("is_active", True)

        if not prompt_type or not template:
            return Response({"error": "prompt_type and template are required"}, status=status.HTTP_400_BAD_REQUEST)

        # Read current prompts from JSON
        try:
            with open(PROMPTS_JSON_PATH, "r") as f:
                prompts = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            prompts = {}

        # Deactivate all other prompts of the same type
        for key in prompts:
            if key == prompt_type:
                prompts[key]["is_active"] = False

        # Add/update the prompt, mark active per request
        prompts[prompt_type] = {
            "template": template,
            "is_active": is_active,
            "last_modified": datetime.utcnow().isoformat(),
        }

        # Save back to JSON file
        try:
            with open(PROMPTS_JSON_PATH, "w") as f:
                json.dump(prompts, f, indent=2)
        except Exception as e:
            return Response({"error": f"Failed to save prompts: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            "name": prompt_type,
            "template": template,
            "is_active": is_active,
            "last_modified": prompts[prompt_type]["last_modified"],
        }, status=status.HTTP_201_CREATED)




@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_organization(request):
    user = request.user
    input_org = request.data.get("organization", "").strip().lower()
    actual_org = user.organization.strip().lower() if user.organization else ""

    if input_org == actual_org:
        return Response({"status": "verified", "message": "✅ Organization matched. You are verified."})
    else:
        return Response({"status": "not_verified", "message": "❌ Organization mismatch. Please login with the correct account."}, status=400)



# views.py
# chatbot/views.py

def generate_gpt_summary(text, prompt_prefix, max_tokens=300):
    """
    Uses OpenAI GPT model to generate a concise summary of the given text.

    Args:
        text (str): The input text to summarize.
        prompt_prefix (str): Instruction prompt to guide summarization.
        max_tokens (int): Max tokens for the GPT response.

    Returns:
        str or None: The generated summary text or None on failure.
    """
    prompt = f"{prompt_prefix}\n\n{text}"
    try:
        response = openai.ChatCompletion.create(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
            max_tokens=max_tokens,
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"GPT summary error: {e}")
        return None

def generate_ticket_content(product_info):
    """
    Generate Problem Description and Short Title using GPT-4.0 mini based on product information.

    Args:
        product_info (dict): Dictionary containing product details

    Returns:
        dict: Contains 'problem_description' and 'short_title' or None on failure
    """
    try:
        # Create a comprehensive prompt for generating ticket content
        prompt = f"""
Based on the following product information, generate a professional problem description and short title for a support ticket:

Product Type: {product_info.get('product_type', 'N/A')}
Product Name: {product_info.get('product_name', 'N/A')}
Model: {product_info.get('model', 'N/A')}
Serial Number: {product_info.get('serial_no', 'N/A')}
Operating System: {product_info.get('operating_system', 'N/A')}
Purchased From: {product_info.get('purchased_from', 'N/A')}
Year of Purchase: {product_info.get('year_of_purchase', 'N/A')}
PO Number: {product_info.get('po_number', 'N/A')}

Please generate:
1. A short title (max 60 characters) that summarizes the potential support need
2. A problem description (2-3 sentences) that describes common issues or setup requirements for this product

Format your response as:
SHORT_TITLE: [title here]
PROBLEM_DESCRIPTION: [description here]
"""

        response = openai.ChatCompletion.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
            max_tokens=500,
        )

        content = response.choices[0].message.content.strip()

        # Parse the response
        lines = content.split('\n')
        short_title = ""
        problem_description = ""

        for line in lines:
            if line.startswith("SHORT_TITLE:"):
                short_title = line.replace("SHORT_TITLE:", "").strip()
            elif line.startswith("PROBLEM_DESCRIPTION:"):
                problem_description = line.replace("PROBLEM_DESCRIPTION:", "").strip()

        # Fallback if parsing fails
        if not short_title:
            short_title = f"{product_info.get('product_name', 'Product')} - {product_info.get('model', 'Support')}"

        if not problem_description:
            problem_description = f"Support request for {product_info.get('product_name', 'product')} {product_info.get('model', 'model')}. Customer needs assistance with setup, configuration, or troubleshooting."

        return {
            'short_title': short_title[:120],  # Ensure it fits the field limit
            'problem_description': problem_description
        }

    except Exception as e:
        print(f"GPT ticket content generation error: {e}")
        # Return fallback content
        return {
            'short_title': f"{product_info.get('product_name', 'Product')} - {product_info.get('model', 'Support')}",
            'problem_description': f"Support request for {product_info.get('product_name', 'product')} {product_info.get('model', 'model')}. Customer needs assistance with setup, configuration, or troubleshooting."
        }
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    user = request.user
    print("Logged-in user:", request.user)
    print("Name:", user.name)
  
    return Response({
        "name": user.name,  # ✅ This should match your DB column
        "email": user.official_email,
        "organization": user.organization
    })

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .serializers import SupportTicketSerializer

 # import your GPT helper function here

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_ticket(request):
    # Extract product information from request
    product_info = {
        'product_type': request.data.get('product_type', ''),
        'product_name': request.data.get('product_name', ''),
        'model': request.data.get('model', ''),
        'serial_no': request.data.get('serial_no', ''),
        'operating_system': request.data.get('operating_system', ''),
        'purchased_from': request.data.get('purchased_from', ''),
        'year_of_purchase': request.data.get('year_of_purchase', ''),
        'po_number': request.data.get('po_number', ''),
    }

    # Generate Problem Description and Short Title using GPT-4.0 mini
    generated_content = generate_ticket_content(product_info)

    # Add generated content to the request data
    ticket_data = request.data.copy()
    ticket_data['problem_description'] = generated_content['problem_description']
    ticket_data['short_title'] = generated_content['short_title']

    serializer = SupportTicketSerializer(data=ticket_data)

    if serializer.is_valid():
        # Save ticket with AI-generated content
        ticket = serializer.save(user=request.user)

        # Generate problem summary from the AI-generated problem description
        problem_summary = generate_gpt_summary(
            ticket.problem_description,
            "Summarize this problem description clearly and professionally:"
        )
        if problem_summary:
            ticket.problem_summary = problem_summary
            ticket.save(update_fields=["problem_summary"])

        return Response({
            "status": "success",
            "message": "Ticket created successfully.",
            "ticket_number": ticket.ticket_number,
            "generated_content": {
                "short_title": ticket.short_title,
                "problem_description": ticket.problem_description
            }
        })
    else:
        return Response({"status": "error", "errors": serializer.errors}, status=400)



from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from chatbot.models import SupportTicket

# Your API key here or from environment/config
openai.api_key = "********************************************************************************************************************************************************************"

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from openai import OpenAIError  # Import specific OpenAI error

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from openai import OpenAIError
from collections import Counter

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_problem_description(request):
    ticket_number = request.data.get("ticket_number")
    raw_description = (request.data.get("problem_description") or "").strip()

    if not ticket_number or not raw_description:
        return Response({"error": "ticket_number and problem_description are required"}, status=400)

    # Fetch ticket that belongs to requesting user
    ticket = get_object_or_404(SupportTicket, ticket_number=ticket_number, user=request.user)

    # Check if ticket is in a valid state
    if ticket.status == "closed":
        return Response({"error": "Cannot add problem description to a closed ticket"}, status=400)

    # 1️⃣ Rewrite description
    try:
        rewrite_resp = openai.ChatCompletion.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "Rewrite user complaints professionally."},
                {"role": "user", "content": raw_description},
            ],
        )
        cleaned_description = rewrite_resp.choices[0].message.content.strip()
    except OpenAIError as e:
        print(f"❌ OpenAI error during description rewrite: {e}")
        return Response({"error": f"Failed to rewrite description: {e}"}, status=500)
    except Exception as e:
        print(f"❌ Unexpected error during description rewrite: {e}")
        return Response({"error": f"Unexpected error: {e}"}, status=500)

    # Store refined problem text
    ticket.problem_description = cleaned_description
    ticket.save(update_fields=["problem_description"])

    # 2️⃣ Generate & save problem summary
    try:
        problem_summary = generate_gpt_summary(
            cleaned_description,
            "Summarize the following user problem in 2 lines:"
        ) or "No summary yet."
        ticket.problem_summary = problem_summary.strip()
        ticket.save(update_fields=["problem_summary"])
        print(f"✅ Problem summary saved for ticket {ticket.ticket_number}")
    except OpenAIError as e:
        print(f"❌ OpenAI error during problem summary generation: {e}")
    except Exception as e:
        print(f"❌ Unexpected error during problem summary generation: {e}")

    # 3️⃣ Build query with ticket metadata for accurate retrieval
    db_ctx = {
        "productType": ticket.product_type,
        "purchasedFrom": ticket.purchased_from,
        "yearOfPurchase": ticket.year_of_purchase,
        "productName": ticket.product_name,
        "model": ticket.model,
        "serialNo": ticket.serial_no,
        "operatingSystem": ticket.operating_system,
    }
    full_query = build_full_query(
        user_query=cleaned_description,
        product_ctx=db_ctx,
        problem_description=cleaned_description,
        solution_summary=None
    )

    result = retrieve_and_generate_answer(full_query, user_id=request.user.id, top_k=5)
    answer_text = result.get("answer", "No answer.")
    file_scores = result.get("file_scores", {})

    if file_scores:
        most_relevant_file, best_score = max(file_scores.items(), key=lambda x: x[1])
        file_objs = [{
            "filename": most_relevant_file,
            "url": f"/api/files/{most_relevant_file}",
            "score": best_score
        }]
    else:
        file_objs = []






    # 5️⃣ Generate & save a two-line summary of the answer
    try:
        summary = generate_gpt_summary(
            answer_text,
            "Summarize the following solution in 2 lines:"
        ) or "No solution yet."
        ticket.solution_summary = summary.strip()
        ticket.save(update_fields=["solution_summary"])
        print(f"✅ Solution summary saved for ticket {ticket.ticket_number}")
    except OpenAIError as e:
        print(f"❌ OpenAI error during solution summary generation: {e}")
    except Exception as e:
        print(f"❌ Unexpected error during solution summary generation: {e}")

    # 6️⃣ Return to front-end
    return Response({
        "status": "success",
        "answer": answer_text,
        "files": file_objs,
    })



from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import SupportTicket


from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from chatbot.models import SupportTicket  # Adjust import path as needed

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_ticket_status(request):
    ticket_number = request.data.get("ticket_number")
    status = request.data.get("status")  # Expected: 'open' or 'closed'

    if status not in ["open", "closed"]:
        return Response({"error": "Invalid status."}, status=400)

    if not ticket_number:
        return Response({"error": "Ticket number is required."}, status=400)

    try:
        # Find ticket by ticket_number and user
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=404)

    ticket.status = status
    ticket.save()
    return Response({"success": True, "message": f"Ticket {ticket_number} marked as {status}."})


# === Configuration ===
WEAVIATE_CLASS_NAME = "ChunkEmbeddingsV2"
#OPENAI_API_KEY = "********************************************************************************************************************************************************************"
EMBEDDING_MODEL = "text-embedding-ada-002"
GPT_MODEL = "gpt-4o-mini"

WEAVIATE_URL = "http://localhost:8080"
CACHE_CLASS_NAME = "CachedQuestions"
EMBEDDING_MODEL = "text-embedding-ada-002"
GPT_MODEL = "gpt-4o-mini"

client = weaviate.Client(WEAVIATE_URL)
#openai_client = OpenAI(api_key="********************************************************************************************************************************************************************")  # Replace with your actual key

#def get_embedding(text):
    #response = openai_client.embeddings.create(input=text, model=EMBEDDING_MODEL)
   # return response.data[0].embedding
def get_embedding(text):
    response = openai.Embedding.create(input=text, model=EMBEDDING_MODEL)
    return response.data[0].embedding


def search_cache(query_vector, certainty_threshold=0.9):
    try:
        near_vector = {"vector": query_vector, "certainty": certainty_threshold}
        result = client.query.get(CACHE_CLASS_NAME, ["text", "answer", "source_files"]) \
                        .with_near_vector(near_vector) \
                        .with_limit(1) \
                        .do()

        matches = result.get("data", {}).get("Get", {}).get(CACHE_CLASS_NAME, [])
        if matches:
            match = matches[0]
            return {
                "query_text": match["text"],
                "answer": match["answer"],
                "files": match.get("source_files", []),
            }
        return None

    except Exception as e:
        print("❌ Weaviate cache search error:", e)
        return None


# ───────────────────────────────────────────────────────────────────
# OLD:
# def add_to_cache(query_text, answer_text, vector):

def add_to_cache(query_text, answer_text, vector, source_files):
    """
    Store a Q/A plus its vector & related source files list.
    """
    try:
        client.data_object.create(
            data_object={
                "text": query_text,
                "answer": answer_text,
                "source_files": source_files,  # matches schema property name
            },
            class_name=CACHE_CLASS_NAME,
            vector=vector
        )
    except Exception as e:
        print("❌ Weaviate cache insert error:", e)





#client_openai = OpenAI(api_key="********************************************************************************************************************************************************************")
chat_history = []
from django.http import HttpResponse, Http404
from .models import PdfFile  # Replace with your actual model name

from urllib.parse import unquote

@api_view(['GET'])
def serve_file_view(request, filename):
    decoded_filename = unquote(filename)  # Decode %20, %28, %29 etc
    try:
        file_entry = PdfFile.objects.get(file_name=decoded_filename)
        response = HttpResponse(file_entry.file_data, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{decoded_filename}"'
        return response
    except PdfFile.DoesNotExist:
        raise Http404(f"No such file: {decoded_filename}")



# === In-memory per-user chat history (not persistent) ===
chat_history_per_user = {}

def get_user_history(user_id):
    return chat_history_per_user.get(user_id, [])

def add_to_user_history(user_id, query, answer):
    history = chat_history_per_user.get(user_id, [])
    history.append((query, answer))
    if len(history) > 20:
        history = history[-20:]
    chat_history_per_user[user_id] = history

# === Signup View ===

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import CustomUser
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.models import User
import json

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.views import TokenObtainPairView
from .serializers import EmailTokenObtainPairSerializer
from rest_framework.permissions import IsAuthenticated
User = get_user_model()


@api_view(['POST'])
@permission_classes([AllowAny])
def signup_view(request):
    data = request.data

    # Explicit required fields validation
    required_fields = ['state', 'name', 'address', 'organization', 'official_email', 'phone', 'mobile', 'password', 'password2']

    for field in required_fields:
        if not data.get(field):
            return Response({"error": f"Missing required field: {field}"}, status=status.HTTP_400_BAD_REQUEST)

    # Password match check
    if data['password'] != data['password2']:
        return Response({"error": "Passwords do not match"}, status=status.HTTP_400_BAD_REQUEST)

    # Check if user exists
    if User.objects.filter(official_email=data['official_email']).exists():
        return Response({"error": "User with this email already exists"}, status=status.HTTP_400_BAD_REQUEST)

    # Create user with provided data
    user = User.objects.create_user(
        official_email=data['official_email'],
        password=data['password'],
        state=data['state'],
        name=data['name'],
        address=data['address'],
        organization=data['organization'],
        alt_email=data.get('alt_email', ''),  # optional field
        phone=data['phone'],
        mobile=data['mobile'],
    )

    return Response({"message": "User created successfully"}, status=status.HTTP_201_CREATED)

class EmailLoginView(TokenObtainPairView):
    serializer_class = EmailTokenObtainPairSerializer


from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from datetime import datetime
from .upload import upload_pdf_to_db  # Your upload logic

from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse

@csrf_exempt
def upload_pdf_view(request):
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)

    try:
        files = request.FILES.getlist('pdf_file')
        if not files:
            return JsonResponse({'error': 'No files received'}, status=400)

        success_count = 0
        for file in files:
            binary_data = file.read()
            filename = file.name
            uploaded = upload_pdf_to_db(filename, binary_data, datetime.now())
            if uploaded:
                success_count += 1

        return JsonResponse({'message': f'{success_count} file(s) uploaded successfully'}, status=200)
    except Exception as e:
        return JsonResponse({'error': f'Exception: {str(e)}'}, status=500)

    
    

from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import get_authorization_header
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.http import HttpResponse, Http404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from urllib.parse import unquote

from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework import exceptions

@api_view(['GET'])
@permission_classes([AllowAny])
def serve_file_view(request, filename):
    # Try to authenticate from ?token= query param
    token = request.query_params.get('token')
    if token:
        try:
            validated_token = JWTAuthentication().get_validated_token(token)
            user = JWTAuthentication().get_user(validated_token)
            request.user = user
        except exceptions.AuthenticationFailed:
            return Response({"detail": "Invalid token."}, status=401)
    else:
        # You can also check request.headers for 'Authorization' if you want
        # or reject unauthenticated requests
        return Response({"detail": "Authentication credentials were not provided."}, status=401)

    decoded_filename = unquote(filename)
    try:
        file_entry = PdfFile.objects.get(file_name=decoded_filename)
        response = HttpResponse(file_entry.file_data, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{decoded_filename}"'
        return response
    except PdfFile.DoesNotExist:
        raise Http404(f"No such file: {decoded_filename}")




def get_prompt_from_json(prompt_type):
    PROMPTS_JSON_PATH = r"D:\AI-Agent-Chatbot-main\chatbot\prompt.json"
    try:
        with open(PROMPTS_JSON_PATH, "r") as f:
            prompts = json.load(f)
        prompt_data = prompts.get(prompt_type)
        if not prompt_data or not prompt_data.get("is_active"):
            raise ValueError(f"No active prompt found for type: {prompt_type}")
        return prompt_data["template"]
    except Exception as e:
        raise ValueError(f"Prompt loading error: {str(e)}")

# === OpenAI + Weaviate helper functions ===

def get_embedding(text):
    response = openai.Embedding.create(input=text, model=EMBEDDING_MODEL)
    return response.data[0].embedding

def generate_answer(query, context_chunks, history_tuples):
    context_text = "\n".join(context_chunks)
    history_text = "\n".join(f"Q: {q}\nA: {a}" for q, a in history_tuples[-5:])

    try:
        prompt_template = get_prompt_from_json("chat")  # Use "chat" or any type you use in your JSON
    except Exception as e:
        return f"❌ Prompt Error: {str(e)}"

    filled_prompt = (
        prompt_template
        .replace("{context_text}", context_text)
        .replace("{history_text}", history_text)
        .replace("{query}", query)
    )

    try:
        completion = openai.ChatCompletion.create(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": filled_prompt}],
            temperature=0.3,
            max_tokens=2000  # ✅ add this to allow full-length answers
        )
        return completion.choices[0].message.content.strip()
    except Exception as e:
        return f"❌ OpenAI error: {e}"



def search_similar_chunks_weaviate(query, limit=5):
    try:
        query_embedding = get_embedding(query)
        client = weaviate.Client(url="http://localhost:8080")

        schema = client.schema.get()
        classes = [cls["class"] for cls in schema.get("classes", [])]
        if WEAVIATE_CLASS_NAME not in classes:
            print(f"❌ Collection '{WEAVIATE_CLASS_NAME}' not found in Weaviate")
            return []

        response = (
            client.query
            .get(WEAVIATE_CLASS_NAME, ["source_file", "chunk_number", "content"])
            .with_near_vector({"vector": query_embedding, "certainty": 0.85})
            .with_limit(limit)
            .do()
        )

        results = []
        for obj in response.get("data", {}).get("Get", {}).get(WEAVIATE_CLASS_NAME, []):
            results.append({
                "source_file": obj.get("source_file", "Unknown"),
                "chunk_number": obj.get("chunk_number", 0),
                "content": obj.get("content", "")
            })
        return results

    except Exception as e:
        print(f"❌ Weaviate error: {e}")
        return []

from collections import defaultdict

from collections import defaultdict

LOG_FILE_PATH = "debug_log.txt"  # Change YourUserName accordingly

def log_debug(message: str):
    with open(LOG_FILE_PATH, "a", encoding="utf-8") as f:
        f.write(message + "\n")

def retrieve_and_generate_answer(query_text, user_id, top_k=5):
    matches = search_similar_chunks_weaviate(query_text, limit=top_k)
    if not matches:
        log_debug("DEBUG ▸ No matches found.")
        return {
            "answer": "Please ask query related to Online Solutions products and services.",
            "matches": [],
            "file_suggestions": []
        }

    context_chunks = [match["content"] for match in matches]
    history_tuples = get_user_history(user_id)

    answer = generate_answer(query_text, context_chunks, history_tuples)
    add_to_user_history(user_id, query_text, answer)

    # Score files by sum of certainty
    file_scores = defaultdict(float)
    for match in matches:
        f = match.get("source_file")
        if f and "certainty" in match:
            file_scores[f] += match["certainty"]

    # Log all file scores
    log_debug("DEBUG ▸ File scores:")
    for file, score in file_scores.items():
        log_debug(f"  {file}: {score}")

    if file_scores:
        # pick file with highest score
        best_file = max(file_scores.items(), key=lambda x: x[1])[0]
        log_debug(f"DEBUG ▸ Selected best file: {best_file} with score {file_scores[best_file]}")
        file_suggestions = [best_file]
    else:
        log_debug("DEBUG ▸ No files scored.")
        file_suggestions = []

    return {
        "answer": answer,
        "matches": matches,
        "file_suggestions": file_suggestions
    }


# === Chat Endpoint (protected) ===

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from chatbot.models import SupportTicket
from collections import Counter



# ── helper --------------------------------------------------------------

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from scipy.spatial.distance import cosine
from collections import Counter

def build_full_query(user_query, product_ctx, problem_description=None, solution_summary=None):
    """
    Build a rich query string by combining user query, product context, problem description, and solution summary.
    
    Args:
        user_query (str): The user's input query.
        product_ctx (dict): Dictionary containing product details (e.g., productType, model).
        problem_description (str, optional): The ticket's problem description.
        solution_summary (str, optional): The ticket's solution summary.
    
    Returns:
        str: A formatted query string with all relevant context.
    """
    ctx_lines = [
        f"Product Type: {product_ctx.get('productType', '')}",
        f"Purchased From: {product_ctx.get('purchasedFrom', '')}",
        f"Year of Purchase: {product_ctx.get('yearOfPurchase', '')}",
        f"Product Name: {product_ctx.get('productName', '')}",
        f"Model: {product_ctx.get('model', '')}",
        f"Serial Number: {product_ctx.get('serialNo', '')}",
        f"Operating System: {product_ctx.get('operatingSystem', '')}",
    ]
    if problem_description:
        ctx_lines.append(f"Problem Description: {problem_description}")
    if solution_summary:
        ctx_lines.append(f"Previous Solution: {solution_summary}")
    context = "\n".join([line for line in ctx_lines if line])
    return f"{context}\n\nUser Query: {user_query}"

from scipy.spatial.distance import cosine

def is_query_related(query_text, ticket, similarity_threshold=0.70):
    """
    Check if the query is related to the ticket's problem description, metadata, and solution summary.
    Returns True if related or if the query is a clarification request, False otherwise.
    """
    if not query_text or not ticket:
        return False

    # List of clarification keywords that are inherently related to the ticket
    clarification_keywords = [
        "elaborate", "more details", "explain", "clarify", "further", "more info",
        "expand", "detail", "describe", "tell me more"
    ]
    if any(keyword in query_text.lower() for keyword in clarification_keywords):
        print(f"DEBUG ▸ Query '{query_text}' identified as a clarification request")
        return True

    # Combine ticket metadata, problem description, and solution summary
    metadata = [
        ticket.product_type or "",
        ticket.purchased_from or "",
        ticket.year_of_purchase or "",
        ticket.product_name or "",
        ticket.model or "",
        ticket.serial_no or "",
        ticket.operating_system or "",
        ticket.problem_description or "",
        ticket.solution_summary or "",
    ]
    combined_text = "\n".join([field for field in metadata if field])

    if not combined_text:
        return False

    try:
        query_embedding = get_embedding(query_text)
        ticket_embedding = get_embedding(combined_text)
        similarity = 1 - cosine(query_embedding, ticket_embedding)
        print(f"DEBUG ▸ Query similarity score: {similarity}")
        return similarity >= similarity_threshold
    except Exception as e:
        print(f"Error checking query relevance: {e}")
        return False
    
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from scipy.spatial.distance import cosine

@api_view(["POST"])
@permission_classes([IsAuthenticated])
def chat(request):
    """
    POST body expected:
    {
        "query"          : "user text",
        "ticket_id"      : "TCKT-123...",
        "ticket_mode"    : true | false,
        "stage"          : "await_close" | "unrelated_query" | "create_new_ticket" | "",
        "product_context": { ... }            # optional – keys see build_full_query()
    }
    """
    user = request.user
    query = (request.data.get("query") or "").strip()
    stage = request.data.get("stage", "")
    tmode = bool(request.data.get("ticket_mode", False))
    tid = request.data.get("ticket_id")
    pctx = request.data.get("product_context") or {}

    if not query:
        return Response({"error": "Query is required"}, status=400)

    print(f"\n=== DEBUG ▸ New chat call – user:{user.id} ticket_mode:{tmode} stage:{stage} query:{query}")

    # ── ticket fetch (if ticket_mode) ────────────────────────────────────
    ticket = None
    if tmode and tid:
        try:
            ticket = SupportTicket.objects.get(ticket_number=tid, user=user)
            print("DEBUG ▸ Ticket found:", ticket.ticket_number)
        except SupportTicket.DoesNotExist:
            print("DEBUG ▸ Ticket NOT found:", tid)
            return Response({"error": "Invalid ticket ID"}, status=400)

    # ── handle unrelated query response ──────────────────────────────────
    if stage == "unrelated_query" and ticket:
        low = query.lower()
        if low == "yes":
            # User wants to create a new ticket
            return Response({
                "answer": "Alright, let's create a new support ticket. Please provide the product type.",
                "ticket_status": ticket.status,
                "stage": "create_new_ticket",
                "files": [],
            })
        elif low == "no":
            # Ask if the user wants to close the current ticket
            return Response({
                "answer": "Okay, do you want to close the current ticket? (yes/no)",
                "ticket_status": ticket.status,
                "stage": "await_close",
                "files": [],
            })
        else:
            return Response({
                "answer": "Please answer 'yes' or 'no'. Do you want to create a new ticket?",
                "ticket_status": ticket.status,
                "stage": "unrelated_query",
                "files": [],
            })

    # ── close / escalate branch ──────────────────────────────────────────
    if stage == "await_close" and ticket:
        low = query.lower()
        if "escalate" in low:
            ticket.status = "escalated"
            ticket.save(update_fields=["status"])
            return Response({
                "answer": "Your ticket has been escalated. The technical support team will contact you ASAP.",
                "ticket_status": "escalated",
                "stage": "",
                "files": [],
            })
        if low in {"yes", "y", "close"}:
            ticket.status = "closed"
            ticket.save(update_fields=["status"])
            return Response({
                "answer": f"✅ Ticket {ticket.ticket_number} has been closed. Thank you!",
                "ticket_status": "closed",
                "stage": "",
                "files": [],
            })
        return Response({
            "answer": "Okay, ticket will remain open.",
            "ticket_status": "open",
            "stage": "",
            "files": [],
        })

    # ── build a single rich query string ─────────────────────────────────
    if ticket:
        db_ctx = {
            "productType":     ticket.product_type,
            "purchasedFrom":   ticket.purchased_from,
            "yearOfPurchase":  ticket.year_of_purchase,
            "productName":     ticket.product_name,
            "model":           ticket.model,
            "serialNo":        ticket.serial_no,
            "operatingSystem": ticket.operating_system,
        }
        full_query = build_full_query(
            user_query=query,
            product_ctx=db_ctx,
            problem_description=ticket.problem_description,
            solution_summary=ticket.solution_summary or "",  # Include solution_summary
        )
    else:
        full_query = build_full_query(
            user_query=query,
            product_ctx=pctx,
            problem_description=pctx.get("problemDescription"),
        )

    # ── check if query is related to ticket (in ticket mode, after problem description) ─────────────
    if tmode and ticket and ticket.problem_description and query != "Please help me with this issue":
        print(f"DEBUG ▸ Checking if query '{query}' is related to ticket {ticket.ticket_number}")
        print(f"DEBUG ▸ Ticket problem description: {ticket.problem_description[:100]}...")
        if not is_query_related(query, ticket):
            print("DEBUG ▸ Query unrelated to ticket:", ticket.ticket_number)
            return Response({
                "answer": f"Your query seems unrelated to ticket {ticket.ticket_number}. Would you like to create a new ticket for this issue?",
                "ticket_status": ticket.status,
                "stage": "unrelated_query",
                "files": [],
            })
        else:
            print("DEBUG ▸ Query is related to ticket, proceeding with normal processing")

    # ── cache check ──────────────────────────────────────────────────────
    if not tmode:
        print("DEBUG ▸ Ticket mode is OFF - running cache check…")
        vec = get_embedding(full_query)
        cached = search_cache(vec, certainty_threshold=0.9)
        if cached:
            print("DEBUG ▸ Cache HIT - returning cached answer")
            return Response({
                "query": query,
                "answer": cached["answer"],
                "source": "cache",
                "cached_query": cached["query_text"],
                "matches": [],
                "files": cached["files"],
                "stage": "",
            })
        print("DEBUG ▸ Cache MISS - no cached answer found")
    else:
        print("DEBUG ▸ Ticket mode is ON - skipping cache check and querying GPT fresh")

    # ── retrieval + LLM answer ───────────────────────────────────────────
    print("DEBUG ▸ Querying Weaviate for retrieval chunks…")
    matches = search_similar_chunks_weaviate(full_query, limit=5)
    print(f"DEBUG ▸ {len(matches)} chunks retrieved")

    context_chunks = [m["content"] for m in matches]
    history = get_user_history(user.id)

    print("DEBUG ▸ Sending prompt to GPT-4 for answer generation…")
    answer = generate_answer(full_query, context_chunks, history)
    print("DEBUG ▸ GPT answer generated (length):", len(answer))
    add_to_user_history(user.id, query, answer)

    # ── related files ────────────────────────────────────────────────────
    pdfs = {
        (m.get("source_file") or "").strip()
        for m in matches
        if isinstance(m.get("source_file"), str)
           and m.get("source_file", "").lower().endswith(".pdf")
    }
    file_objs = [{"filename": f, "url": f"/api/files/{f}"} for f in sorted(pdfs) if f]

    # ── save two-line summary (ticket mode) ──────────────────────────────
    if ticket:
        try:
            summ_src = answer if answer and len(answer.strip()) >= 10 else f"Brief answer:\n{answer}"
            summ = generate_gpt_summary(summ_src, "Summarize in 2 lines:")
            ticket.solution_summary = (summ or "No solution yet.")
            ticket.save(update_fields=["solution_summary"])
            print(f"✅ Solution summary saved for ticket {ticket.ticket_number}")
        except Exception as e:
            print("DEBUG ▸ summary save error:", e)

    # ── Enhanced follow-up handling ───────────────────────────────────────────────
    follow_up = ""
    next_stage = ""

    # Check if the answer contains technical terms that might need explanation
    technical_terms = ["CAM Expert", "SDK", "API", "driver", "firmware", "calibration", "exposure", "gain"]
    contains_technical_terms = any(term.lower() in answer.lower() for term in technical_terms)

    if file_objs:
        # If files available, front-end will handle file download prompt
        follow_up = "\n\n💡 For full explanation, do you want the related file?"
    elif ticket and contains_technical_terms:
        # If technical terms are mentioned, offer more detailed explanation
        follow_up = "\n\n🔍 This solution contains technical terms. Do you need more detailed explanations of any specific terms mentioned?"
    elif ticket:
        # For ticket mode without files, ask for other queries
        follow_up = "\n\n🟢 Do you have any other queries about this ticket?"
        next_stage = "await_close"

    # ── final response ───────────────────────────────────────────────────
    return Response({
        "query": query,
        "answer": answer + follow_up,
        "matches": matches,
        "files": file_objs,
        "source": "gpt",
        "stage": next_stage,
        "ticket_context": {
            "ticket_number": ticket.ticket_number if ticket else None,
            "product_info": f"{ticket.product_name} - {ticket.model}" if ticket and ticket.product_name and ticket.model else None,
        } if ticket else None,
    })




from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from .models import SupportTicket
from .serializers import (
    EscalatedTicketListSerializer,
    EscalatedTicketDetailSerializer,
)
@api_view(["GET"])
@permission_classes([IsAdminUser])
def escalated_tickets(request):
    qs = SupportTicket.objects.filter(status="escalated").order_by("-created_at")
    data = EscalatedTicketListSerializer(qs, many=True).data
    return Response(data)


@api_view(["GET"])
@permission_classes([IsAdminUser])
def escalated_ticket_detail(request, ticket_number):
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number)
    except SupportTicket.DoesNotExist:
        return Response({"detail": "Ticket not found."}, status=404)
    data = EscalatedTicketDetailSerializer(ticket).data
    return Response(data)



import os
import subprocess
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response

@api_view(["POST"])
@permission_classes([IsAdminUser])
def run_processing_pipeline(request):
    """
    Processing pipeline:
    1. Chunk PDFs (chunking.py)
    2. Push vectors to Weaviate (vector_embedding.py)
    All scripts must be located inside the 'chatbot' folder.
    """

    # Get absolute path to your Django project root
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    chatbot_dir = os.path.join(BASE_DIR, "chatbot")

    # Use current Python interpreter (works with any environment)
    import sys
    venv_python = sys.executable

    # Define scripts in execution order
    scripts = [
        # {"name": "img2.py", "args": []},
        {"name": "chunking.py", "args": []},
        {"name": "vector_embedding.py", "args": ["--weaviate"]},
    ]

    for script in scripts:
        script_name = script["name"]
        script_args = script["args"]
        script_path = os.path.join(chatbot_dir, script_name)

        if os.path.exists(script_path):
            try:
                command = [venv_python, script_path] + script_args
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    check=True
                )
                print(f"✅ Script {script_name} completed:\n{result.stdout}")
            except subprocess.CalledProcessError as e:
                print(f"❌ Script {script_name} failed:\n{e.stderr}")
                return Response(
                    {"detail": f"Script {script_name} failed", "error": e.stderr},
                    status=500
                )
        else:
            print(f"❌ Script not found: {script_path}")
            return Response(
                {"detail": f"Script not found: {script_name}"},
                status=400
            )

    return Response({"detail": "Processing pipeline completed."}, status=200)



# === Health Check ===

@api_view(['GET'])
@permission_classes([AllowAny])
def health(request):
    weaviate_status = "unavailable"
    collections = []
    counts = {}

    try:
        client = weaviate.Client(url="http://localhost:8080")
        schema = client.schema.get()
        if 'classes' in schema:
            collections = [cls['class'] for cls in schema['classes']]
            for collection in collections:
                result = client.query.aggregate(collection).with_meta_count().do()
                if result and "data" in result and "Aggregate" in result["data"]:
                    count = result["data"]["Aggregate"][collection][0]["meta"]["count"]
                    counts[collection] = count
            weaviate_status = "connected"
        client.close()
    except Exception as e:
        weaviate_status = f"error: {str(e)}"

    return Response({
        "status": "healthy",
        "weaviate_status": weaviate_status,
        "collections": collections,
        "counts": counts
    })


# === Home API ===

@api_view(['GET'])
@permission_classes([AllowAny])
def home(request):
    return Response({
        "message": "AI Agent Chatbot Backend API with RAG (Django)",
        "version": "2.0",
        "framework": "Django + Django REST Framework",
        "endpoints": {
            "/api/chat/": "POST - AI-powered chat with context (RAG) [Requires Authentication]",
            "/api/signup/": "POST - Signup new user",
            "/api/token/": "POST - Login (JWT token obtain)",
            "/api/token/refresh/": "POST - Refresh JWT token",
            "/api/health/": "GET - Health check",
            "/api/upload_pdf/": "POST - Upload PDF files"
        },
        "features": [
            "JWT Authentication",
            "GPT-4o-mini powered answers",
            "Retrieval Augmented Generation (RAG)",
            "Weaviate vector database integration",
            "Technical documentation expertise"
        ]
    })